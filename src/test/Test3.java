package test;

import java.util.Arrays;
import java.util.List;

public class Test3 {
    // 源用户ID
    private static final Integer SOURCE_USER_ID = 1015891;

    // 目标用户ID列表
    private static final List<Integer> TARGET_USER_IDS = Arrays.asList(
        1015900, 1015901, 1015902, 1015903, 1015904, 1015905, 1015906, 1015907, 1015908, 1015909,
        1015910, 1015911, 1015912, 1015913, 1015914, 1015915, 1015916, 1015917, 1015918, 1015919,
        1015920, 1015921, 1015922, 1015923, 1015924, 1015925, 1015926, 1015927, 1015928, 1015929
    );

    // trace_id列表
    private static final List<String> TRACE_IDS = Arrays.asList(
        "010158911753197420UAQtW3YcMsa",
        "010158911753197937oI6z6QztIA3",
        "010158911753198132jnet0vWNAZR",
        "010158911753197607isQmml4XGZk",
        "01015891175320453181m81w47yR0",
        "01015891175320341241duSlZ9Lp3",
        "010158911753199740oDIGA5eTGIh",
        "010158911753198583TIIbsRCguH5",
        "0101589117531989910wsDd9q98T2",
        "010158911753198762UriqpU3YD0P"
    );

    public static void main(String[] args) {
        System.out.println("=== 数据复制SQL语句生成 ===\n");

        // 获取源用户的表信息
        TableInfo sourceTableInfo = getTableInfo(SOURCE_USER_ID);
        System.out.println("源用户表信息:");
        System.out.println("Database: " + sourceTableInfo.dbName);
        System.out.println("Video Library: " + sourceTableInfo.videoLibraryTable);
        System.out.println("Library Status: " + sourceTableInfo.libraryStatusTable);
        System.out.println("Video Slice: " + sourceTableInfo.videoSliceTable);
        System.out.println();

        // 为每个目标用户生成复制SQL
        for (Integer targetUserId : TARGET_USER_IDS) {
            System.out.println("=== 复制数据到用户 " + targetUserId + " ===");
            generateCopyStatements(sourceTableInfo, targetUserId);
            System.out.println();
        }
    }

    /**
     * 获取用户对应的表信息
     */
    public static TableInfo getTableInfo(Integer userId) {
        String dbName = "`video-library-db-" + Integer.parseInt(String.valueOf(userId % 8192 / 1024)) + "`";
        String videoLibraryTable = dbName + ".`video_library_" + String.format("%02d", userId % 64) + "`";
        String libraryStatusTable = dbName + ".`library_status_" + String.format("%02d", userId % 64) + "`";
        String videoSliceTable = dbName + ".`video_slice_" + String.format("%03d", userId % 512) + "`";

        return new TableInfo(dbName, videoLibraryTable, libraryStatusTable, videoSliceTable);
    }

    /**
     * 生成数据复制语句
     */
    public static void generateCopyStatements(TableInfo sourceTableInfo, Integer targetUserId) {
        TableInfo targetTableInfo = getTableInfo(targetUserId);

        // 生成trace_id的IN子句
        String traceIdInClause = generateTraceIdInClause();

        // 1. 复制video_library数据
        System.out.println("-- 复制video_library数据");
        String videoLibraryCopySQL = String.format(
            "INSERT INTO %s \n" +
            "SELECT \n" +
            "    %d as user_id,  -- 更新为目标用户ID\n" +
            "    trace_id,\n" +
            "    -- 这里需要根据实际表结构添加其他字段\n" +
            "    -- 假设还有其他字段，请根据实际情况调整\n" +
            "FROM %s \n" +
            "WHERE user_id = '%d' AND trace_id IN %s;",
            targetTableInfo.videoLibraryTable,
            targetUserId,
            sourceTableInfo.videoLibraryTable,
            SOURCE_USER_ID,
            traceIdInClause
        );
        System.out.println(videoLibraryCopySQL);
        System.out.println();

        // 2. 复制library_status数据
        System.out.println("-- 复制library_status数据");
        String libraryStatusCopySQL = String.format(
            "INSERT INTO %s \n" +
            "SELECT \n" +
            "    %d as user_id,  -- 更新为目标用户ID\n" +
            "    trace_id,\n" +
            "    -- 这里需要根据实际表结构添加其他字段\n" +
            "    -- 假设还有其他字段，请根据实际情况调整\n" +
            "FROM %s \n" +
            "WHERE user_id = '%d' AND trace_id IN %s;",
            targetTableInfo.libraryStatusTable,
            targetUserId,
            sourceTableInfo.libraryStatusTable,
            SOURCE_USER_ID,
            traceIdInClause
        );
        System.out.println(libraryStatusCopySQL);
        System.out.println();

        // 3. 复制video_slice数据（注意：一对多关系）
        System.out.println("-- 复制video_slice数据（一对多关系）");
        String videoSliceCopySQL = String.format(
            "INSERT INTO %s \n" +
            "SELECT \n" +
            "    %d as user_id,  -- 更新为目标用户ID\n" +
            "    trace_id,\n" +
            "    -- 这里需要根据实际表结构添加其他字段\n" +
            "    -- 假设还有slice_id, slice_data等字段，请根据实际情况调整\n" +
            "FROM %s \n" +
            "WHERE user_id = '%d' AND trace_id IN %s;",
            targetTableInfo.videoSliceTable,
            targetUserId,
            sourceTableInfo.videoSliceTable,
            SOURCE_USER_ID,
            traceIdInClause
        );
        System.out.println(videoSliceCopySQL);
    }

    /**
     * 生成trace_id的IN子句
     */
    private static String generateTraceIdInClause() {
        StringBuilder sb = new StringBuilder("(\n");
        for (int i = 0; i < TRACE_IDS.size(); i++) {
            sb.append("    '").append(TRACE_IDS.get(i)).append("'");
            if (i < TRACE_IDS.size() - 1) {
                sb.append(",");
            }
            sb.append("\n");
        }
        sb.append("  )");
        return sb.toString();
    }

    /**
     * 表信息类
     */
    static class TableInfo {
        String dbName;
        String videoLibraryTable;
        String libraryStatusTable;
        String videoSliceTable;

        public TableInfo(String dbName, String videoLibraryTable, String libraryStatusTable, String videoSliceTable) {
            this.dbName = dbName;
            this.videoLibraryTable = videoLibraryTable;
            this.libraryStatusTable = libraryStatusTable;
            this.videoSliceTable = videoSliceTable;
        }
    }

    // 保留原有的select方法以供参考
    public static String select(String tableName, Integer userId) {
        return "select * from " + tableName + " where user_id = " + "'" + userId + "'";
    }
}
