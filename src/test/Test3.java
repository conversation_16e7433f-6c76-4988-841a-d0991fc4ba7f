package test;

public class Test3 {
    public static void main(String[] args) {
        Integer userId = 1015891;
        String dbName = "`video-library-db-" + Integer.parseInt(String.valueOf(userId % 8192 / 1024)) + "`.";
        String video_library_name = dbName + "`video_library_" + String.format("%02d", userId % 64) + "`";
        String library_status_name = dbName + "`library_status_" + String.format("%02d", userId % 64) + "`";
        String video_slice_name = dbName + "`video_slice_" + String.format("%03d", userId % 512) + "`";
        System.out.println(select(video_library_name, userId));
        System.out.println(select(library_status_name, userId));
        System.out.println(select(video_slice_name, userId));
    }

    public static String select(String tableName, Integer userId) {
        return "select * from " + tableName + " where user_id = " + "'" + userId + "'";
    }
}
